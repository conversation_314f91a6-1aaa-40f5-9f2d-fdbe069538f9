from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import routers
from routers import health, transactions, budgets, bills, ai_chat
from core.database import init_db

# Create FastAPI app
app = FastAPI(
    title="AI Budget Assistant API",
    description="A comprehensive AI-powered household budgeting application with CrewAI multi-agent system",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
@app.on_event("startup")
async def startup_event():
    """Initialize database and AI systems on startup."""
    await init_db()
    
    # Initialize CrewAI system
    from ai.crew_setup import initialize_crew
    await initialize_crew()

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(transactions.router, prefix="/api/v1", tags=["transactions"])
app.include_router(budgets.router, prefix="/api/v1", tags=["budgets"])
app.include_router(bills.router, prefix="/api/v1", tags=["bills"])
app.include_router(ai_chat.router, prefix="/api/v1", tags=["ai-chat"])

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"}
    )

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "AI Budget Assistant API",
        "version": "2.0.0",
        "features": [
            "Transaction Management",
            "Budget Planning",
            "Bill Tracking",
            "AI-Powered Insights",
            "CrewAI Multi-Agent System",
            "Natural Language Processing",
            "Voice Integration"
        ]
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
